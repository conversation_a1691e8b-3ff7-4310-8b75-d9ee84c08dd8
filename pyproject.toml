[project]
name = "autolodge-retrained-deploy"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "azure-ai-ml>=1.27.1",
    "azure-identity>=1.23.0",
    "azureml-inference-server-http>=1.4.0",
    "importlib-resources>=6.5.2",
    "jupyter>=1.1.1",
    "loguru>=0.7.3",
    "nltk==3.7",
    "pandas==1.4.1",
    "pendulum>=3.1.0",
    "protobuf==3.20",
    "pytest>=8.3.5",
    "python-dotenv>=1.1.0",
    "scikit-learn==1.0.2",
    "scipy==1.8",
    "symspellpy==6.7.6",
    "tensorflow==2.8",
    "tqdm==4.63.0",
]
